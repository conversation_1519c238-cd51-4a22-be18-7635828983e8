#!/usr/bin/env python3
from setuptools import setup, find_packages
import os
from datetime import datetime

# Setup script for agent-server package
# @author: xiangjh
this_directory = os.path.abspath(os.path.dirname(__file__))
with open(os.path.join(this_directory, 'README.md'), encoding='utf-8') as f:
    long_description = f.read()

# Read requirements
def read_requirements(filename):
    with open(filename, 'r', encoding='utf-8') as f:
        return [line.strip() for line in f if line.strip() and not line.startswith('#')]

timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
# version = f"1.0.0+{timestamp}"

setup(
    name="agent-server",
    # version=version,
    author="xiangjh",
    author_email="<EMAIL>",
    description="A comprehensive AI agent framework with LangChain, LangGraph, and MCP support",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/agent-server",
    packages=find_packages(exclude=["tests", "tests.*"],include=["agent_server*", "agent_rags*", "agent_system*"]),
    python_requires=">=3.11",
    entry_points={
        "console_scripts": [
            "agent-server=cli:main",
        ],
    },
    include_package_data=True,
    package_data={
        "agent_server.config": ["*.yml"],  # 包含所有 .yml 文件
    },
    zip_safe=False,
    keywords="ai agent langchain langgraph mcp llm",
    project_urls={
        "Bug Reports": "https://github.com/yourusername/agent-server/issues",
        "Source": "https://github.com/yourusername/agent-server",
        "Documentation": "https://agent-server.readthedocs.io/",
    },
)
